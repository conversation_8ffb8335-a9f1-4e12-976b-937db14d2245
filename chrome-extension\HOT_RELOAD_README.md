# 🔥 Chrome Extension 热重载功能

## 概述

现在您的 Chrome 扩展项目已经配置了完整的热重载开发环境！这意味着您可以：

- ✅ 修改任何扩展文件后自动重载
- ✅ 无需手动刷新扩展
- ✅ 实时查看代码变化效果
- ✅ 提高开发效率

## 🚀 快速开始

### 1. 启动开发环境

**推荐方式：**
```bash
# 在项目根目录执行
npm run extension:dev
```

**或者直接启动：**
```bash
cd chrome-extension
node start-dev.js
```

**Windows 用户：**
```bash
# 双击运行
start-dev.bat
```

### 2. 安装扩展

1. 打开 Chrome 浏览器
2. 访问 `chrome://extensions/`
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择 `chrome-extension` 文件夹

### 3. 开始开发

现在您可以修改任何扩展文件，扩展会自动重载！

## 📁 新增文件

热重载功能添加了以下文件：

```
chrome-extension/
├── hot-reload.js          # 🆕 热重载脚本
├── dev-server.js          # 🆕 开发服务器
├── start-dev.js           # 🆕 开发环境启动脚本
├── start-dev.bat          # 🆕 Windows 启动脚本
├── DEV_GUIDE.md          # 🆕 详细开发指南
└── HOT_RELOAD_README.md  # 🆕 本文件
```

## 🔧 工作原理

1. **开发服务器** (`dev-server.js`) 监听文件变化
2. **热重载脚本** (`hot-reload.js`) 定期检查更新
3. **自动重载** 检测到变化时自动重新加载扩展

## 🌐 服务器端点

- 🔥 热重载服务器: `http://localhost:8080`
- 📊 状态检查: `http://localhost:8080/status`
- 🔄 手动重载: `http://localhost:8080/reload`
- 🌐 主应用服务器: `http://localhost:18080`

## 🛠️ 调试方法

### 1. 弹窗调试
- 右键扩展图标 → "检查弹出式窗口"

### 2. 后台脚本调试
- `chrome://extensions/` → 点击"service worker"

### 3. 内容脚本调试
- 网页 F12 → Console 标签

### 4. 热重载调试
- 查看开发服务器日志
- 检查 `http://localhost:8080/status`

## ⚡ 快捷命令

```bash
# 检查热重载状态
npm run extension:status

# 手动触发重载
npm run extension:reload

# 查看详细状态
curl http://localhost:8080/status | jq
```

## 🧪 测试页面

打开 `test-extension.html` 来测试扩展功能：

```bash
# 在浏览器中打开
chrome-extension/test-extension.html
```

## 🔧 故障排除

### 热重载不工作？

1. **检查开发服务器**
   ```bash
   curl http://localhost:8080/status
   ```

2. **查看浏览器控制台**
   - 应该看到 "🔥 Chrome Extension Hot Reload 已启用"

3. **手动触发重载**
   ```bash
   npm run extension:reload
   ```

### 端口冲突？

修改 `dev-server.js` 中的端口：
```javascript
const server = new ExtensionDevServer({
  port: 8081  // 改为其他端口
});
```

### 扩展无法加载？

1. 检查 `manifest.json` 语法
2. 确保所有文件都存在
3. 查看扩展管理页面的错误信息

## 📝 开发提示

1. **修改配置后重载扩展**
   - 修改 `manifest.json` 后需要重载

2. **查看实时日志**
   - 开发服务器会显示文件变化日志

3. **使用测试页面**
   - `test-extension.html` 可以快速测试功能

4. **调试技巧**
   - 使用 `console.log()` 进行调试
   - 利用 Chrome 开发者工具

## 🎉 享受开发！

现在您有了一个完整的 Chrome 扩展热重载开发环境。修改代码，保存文件，扩展会自动重载 - 就是这么简单！

如果遇到问题，请查看 `DEV_GUIDE.md` 获取更详细的说明。
