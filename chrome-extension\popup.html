<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Prompt Tools</title>
  <link rel="stylesheet" href="popup.css">
</head>
<body>
  <div class="container">
    <!-- 头部 -->
    <div class="header">
      <div class="logo">
        <img src="icons/icon32.png" alt="Prompt Tools">
        <h1>Prompt Tools</h1>
      </div>
      <div class="actions">
        <button id="refreshBtn" class="btn-icon" title="刷新">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polyline points="23 4 23 10 17 10"></polyline>
            <polyline points="1 20 1 14 7 14"></polyline>
            <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"></path>
          </svg>
        </button>
        <button id="openWebBtn" class="btn-icon" title="打开网页版">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
            <polyline points="15,3 21,3 21,9"></polyline>
            <line x1="10" y1="14" x2="21" y2="3"></line>
          </svg>
        </button>
      </div>
    </div>

    <!-- 搜索框 -->
    <div class="search-container">
      <input type="text" id="searchInput" placeholder="搜索提示词..." class="search-input">
      <button id="searchBtn" class="search-btn">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <circle cx="11" cy="11" r="8"></circle>
          <path d="M21 21l-4.35-4.35"></path>
        </svg>
      </button>
    </div>

    <!-- 状态显示 -->
    <div id="statusMessage" class="status-message hidden"></div>

    <!-- 提示词列表 -->
    <div class="prompts-container">
      <div id="loadingSpinner" class="loading">
        <div class="spinner"></div>
        <span>正在加载提示词...</span>
      </div>
      
      <div id="errorMessage" class="error-message hidden">
        <div class="error-icon">⚠️</div>
        <div class="error-text">
          <h3>连接失败</h3>
          <p>无法连接到 Prompt Tools 服务器</p>
          <p>请确保服务器正在运行：<br><code>http://localhost:18080</code></p>
          <button id="retryBtn" class="btn-primary">重试连接</button>
        </div>
      </div>

      <div id="promptsList" class="prompts-list hidden"></div>
      
      <div id="emptyState" class="empty-state hidden">
        <div class="empty-icon">📝</div>
        <h3>暂无提示词</h3>
        <p>点击右上角按钮打开网页版添加提示词</p>
      </div>
    </div>

    <!-- 底部 -->
    <div class="footer">
      <div class="tips">
        💡 右键选中文本可快速添加提示词
      </div>
    </div>
  </div>

  <script src="server-config.js"></script>
  <script src="popup.js"></script>
</body>
</html>
