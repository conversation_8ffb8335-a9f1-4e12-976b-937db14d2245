// Prompt Tools Chrome Extension - Background Script

// 导入服务器配置
importScripts('server-config.js');

class PromptToolsBackground {
  constructor() {
    // 使用配置文件中的API基础URL
    this.config = window.PromptToolsConfig || {};
    this.apiBase = this.config.getApiBaseUrl ? this.config.getApiBaseUrl() : 'http://localhost:18080/api';
    this.requestConfig = this.config.getRequestConfig ? this.config.getRequestConfig() : {};

    console.log('后台脚本初始化 - 当前环境:', this.config.currentEnvironment);
    console.log('API基础URL:', this.apiBase);

    this.init();
  }

  init() {
    this.createContextMenu();
    this.bindEvents();
  }

  createContextMenu() {
    // 创建右键菜单
    chrome.contextMenus.create({
      id: 'addToPromptTools',
      title: '添加到 Prompt Tools',
      contexts: ['selection'],
      documentUrlPatterns: ['http://*/*', 'https://*/*']
    });

    chrome.contextMenus.create({
      id: 'openPromptTools',
      title: '打开 Prompt Tools',
      contexts: ['page'],
      documentUrlPatterns: ['http://*/*', 'https://*/*']
    });
  }

  bindEvents() {
    // 右键菜单点击事件
    chrome.contextMenus.onClicked.addListener((info, tab) => {
      if (info.menuItemId === 'addToPromptTools') {
        this.handleAddPrompt(info, tab);
      } else if (info.menuItemId === 'openPromptTools') {
        this.openPromptTools();
      }
    });

    // 扩展安装事件
    chrome.runtime.onInstalled.addListener((details) => {
      if (details.reason === 'install') {
        this.showWelcomeNotification();
      }
    });

    // 消息监听
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      if (request.action === 'addPrompt') {
        this.addPromptToServer(request.data)
          .then(result => sendResponse({ success: true, data: result }))
          .catch(error => sendResponse({ success: false, error: error.message }));
        return true; // 保持消息通道开放
      }
    });
  }

  async handleAddPrompt(info, tab) {
    const selectedText = info.selectionText;
    if (!selectedText || selectedText.trim().length === 0) {
      this.showNotification('请先选择要添加的文本', 'error');
      return;
    }

    try {
      // 获取页面标题作为提示词名称
      const pageTitle = tab.title || '未命名提示词';
      const pageUrl = tab.url;
      
      // 构造提示词数据
      const promptData = {
        name: this.generatePromptName(selectedText, pageTitle),
        content: selectedText.trim(),
        source: this.extractDomain(pageUrl),
        notes: `来自: ${pageTitle}\n链接: ${pageUrl}`,
        tags: ['浏览器扩展', '网页收集']
      };

      // 发送到服务器
      const result = await this.addPromptToServer(promptData);
      
      this.showNotification('✅ 提示词已成功添加到 Prompt Tools', 'success');
      
    } catch (error) {
      console.error('添加提示词失败:', error);
      this.showNotification('❌ 添加失败: ' + error.message, 'error');
    }
  }

  async addPromptToServer(promptData) {
    try {
      // 使用配置文件中的API端点
      const apiUrl = this.config.getApiEndpoint ?
        this.config.getApiEndpoint('prompts', 'create') :
        `${this.apiBase}/prompts`;

      const headers = this.requestConfig.headers || {
        'Content-Type': 'application/json',
      };

      console.log('发送提示词到服务器:', apiUrl);

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(promptData)
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`服务器错误 (${response.status}): ${errorText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('添加提示词到服务器失败:', error);
      throw error;
    }
  }

  generatePromptName(content, pageTitle) {
    // 从内容中提取前几个词作为名称
    const words = content.trim().split(/\s+/).slice(0, 8).join(' ');
    const maxLength = 50;
    
    if (words.length <= maxLength) {
      return words;
    }
    
    return words.substring(0, maxLength - 3) + '...';
  }

  extractDomain(url) {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname;
    } catch (error) {
      return '未知来源';
    }
  }

  openPromptTools() {
    // 使用配置文件中的基础URL
    const baseUrl = this.config.getCurrentEnvironment ?
      this.config.getCurrentEnvironment().baseUrl :
      'http://localhost:18080';

    console.log('打开 Prompt Tools 网页版:', baseUrl);
    chrome.tabs.create({ url: baseUrl });
  }

  showNotification(message, type = 'info') {
    const iconUrl = type === 'error' ? 'icons/icon32.png' : 'icons/icon32.png';
    
    chrome.notifications.create({
      type: 'basic',
      iconUrl: iconUrl,
      title: 'Prompt Tools',
      message: message
    });
  }

  showWelcomeNotification() {
    chrome.notifications.create({
      type: 'basic',
      iconUrl: 'icons/icon48.png',
      title: 'Prompt Tools 扩展已安装',
      message: '右键选择文本可快速添加提示词，点击扩展图标查看所有提示词。'
    });
  }
}

// 初始化后台脚本
new PromptToolsBackground();
